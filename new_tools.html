<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化测试工具集</title>
    <link rel="shortcut icon" href="https://img.scmttec.com/gw/laiyuemiao/<EMAIL>">
    
    <!-- 引入Vue 3和Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <!-- 引入工具库 -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
        }
        
        .title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .tool-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .tool-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .tool-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .input-label {
            font-weight: 500;
            color: #555;
            font-size: 0.9rem;
        }
        
        .result-area {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            min-height: 100px;
            border: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .tool-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }
        
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 头部 -->
            <header class="header">
                <div class="logo-section">
                    <img src="https://img.scmttec.com/gw/laiyuemiao/<EMAIL>" alt="Logo" class="logo">
                    <h1 class="title">现代化测试工具集</h1>
                </div>
                <div>
                    <el-button type="primary" @click="showAbout = true">
                        <el-icon><InfoFilled /></el-icon>
                        关于
                    </el-button>
                </div>
            </header>
            
            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.totalTools }}</div>
                        <div class="stat-label">工具总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.usageCount }}</div>
                        <div class="stat-label">今日使用次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ stats.onlineUsers }}</div>
                        <div class="stat-label">在线用户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ new Date().toLocaleDateString() }}</div>
                        <div class="stat-label">今日日期</div>
                    </div>
                </div>
                
                <!-- 工具网格 -->
                <div class="tool-grid">
                    <!-- Base64 编解码工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#409EFF"><Key /></el-icon>
                            <h3 class="tool-title">Base64 编解码</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">输入文本：</label>
                                <el-input 
                                    v-model="base64Input" 
                                    type="textarea" 
                                    :rows="4" 
                                    placeholder="请输入要编码或解码的文本">
                                </el-input>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="base64Encode">编码</el-button>
                                <el-button type="success" @click="base64Decode">解码</el-button>
                                <el-button type="info" @click="copyResult('base64')">复制结果</el-button>
                                <el-button @click="clearBase64">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">结果：</label>
                                <div class="result-area" id="base64Result">{{ base64Result }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- JSON 格式化工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#67C23A"><Document /></el-icon>
                            <h3 class="tool-title">JSON 格式化</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">JSON 文本：</label>
                                <el-input 
                                    v-model="jsonInput" 
                                    type="textarea" 
                                    :rows="4" 
                                    placeholder="请输入JSON文本">
                                </el-input>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="formatJson">格式化</el-button>
                                <el-button type="warning" @click="compressJson">压缩</el-button>
                                <el-button type="info" @click="copyResult('json')">复制结果</el-button>
                                <el-button @click="clearJson">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">格式化结果：</label>
                                <div class="result-area" id="jsonResult">{{ jsonResult }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- URL 编解码工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#E6A23C"><Link /></el-icon>
                            <h3 class="tool-title">URL 编解码</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">URL 文本：</label>
                                <el-input 
                                    v-model="urlInput" 
                                    type="textarea" 
                                    :rows="4" 
                                    placeholder="请输入URL">
                                </el-input>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="urlEncode">编码</el-button>
                                <el-button type="success" @click="urlDecode">解码</el-button>
                                <el-button type="info" @click="copyResult('url')">复制结果</el-button>
                                <el-button @click="clearUrl">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">结果：</label>
                                <div class="result-area" id="urlResult">{{ urlResult }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间戳转换工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#F56C6C"><Timer /></el-icon>
                            <h3 class="tool-title">时间戳转换</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">时间戳/日期：</label>
                                <el-input
                                    v-model="timestampInput"
                                    placeholder="输入时间戳或日期时间">
                                </el-input>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="timestampToDate">时间戳转日期</el-button>
                                <el-button type="success" @click="dateToTimestamp">日期转时间戳</el-button>
                                <el-button type="warning" @click="getCurrentTimestamp">获取当前时间戳</el-button>
                                <el-button type="info" @click="copyResult('timestamp')">复制结果</el-button>
                                <el-button @click="clearTimestamp">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">转换结果：</label>
                                <div class="result-area" id="timestampResult">{{ timestampResult }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- MD5加密工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#909399"><Lock /></el-icon>
                            <h3 class="tool-title">MD5 加密</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">原始文本：</label>
                                <el-input
                                    v-model="md5Input"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入要加密的文本">
                                </el-input>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="md5Encrypt">MD5加密</el-button>
                                <el-button type="info" @click="copyResult('md5')">复制结果</el-button>
                                <el-button @click="clearMd5">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">MD5结果：</label>
                                <div class="result-area" id="md5Result">{{ md5Result }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- AES加密解密工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#E6A23C"><Key /></el-icon>
                            <h3 class="tool-title">AES 加密解密</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">密钥：</label>
                                <el-input
                                    v-model="aesKey"
                                    placeholder="请输入AES密钥">
                                </el-input>
                            </div>
                            <div class="input-group">
                                <label class="input-label">文本内容：</label>
                                <el-input
                                    v-model="aesInput"
                                    type="textarea"
                                    :rows="4"
                                    placeholder="请输入要加密或解密的文本">
                                </el-input>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="aesEncrypt">AES加密</el-button>
                                <el-button type="success" @click="aesDecrypt">AES解密</el-button>
                                <el-button type="info" @click="copyResult('aes')">复制结果</el-button>
                                <el-button @click="clearAes">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">结果：</label>
                                <div class="result-area" id="aesResult">{{ aesResult }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 随机数生成工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#67C23A"><Dice /></el-icon>
                            <h3 class="tool-title">随机数生成</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <el-row :gutter="10">
                                    <el-col :span="12">
                                        <label class="input-label">最小值：</label>
                                        <el-input-number v-model="randomMin" :min="0" :max="999999"></el-input-number>
                                    </el-col>
                                    <el-col :span="12">
                                        <label class="input-label">最大值：</label>
                                        <el-input-number v-model="randomMax" :min="1" :max="999999"></el-input-number>
                                    </el-col>
                                </el-row>
                            </div>
                            <div class="input-group">
                                <label class="input-label">生成数量：</label>
                                <el-input-number v-model="randomCount" :min="1" :max="100"></el-input-number>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="generateRandom">生成随机数</el-button>
                                <el-button type="success" @click="generateUUID">生成UUID</el-button>
                                <el-button type="info" @click="copyResult('random')">复制结果</el-button>
                                <el-button @click="clearRandom">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">生成结果：</label>
                                <div class="result-area" id="randomResult">{{ randomResult }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 颜色转换工具 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <el-icon color="#F56C6C"><Brush /></el-icon>
                            <h3 class="tool-title">颜色转换</h3>
                        </div>
                        <div class="tool-content">
                            <div class="input-group">
                                <label class="input-label">颜色值：</label>
                                <el-input
                                    v-model="colorInput"
                                    placeholder="输入HEX、RGB或HSL颜色值">
                                </el-input>
                            </div>
                            <div class="input-group">
                                <label class="input-label">颜色预览：</label>
                                <div :style="{backgroundColor: colorPreview, width: '100%', height: '50px', borderRadius: '8px', border: '1px solid #ddd'}"></div>
                            </div>
                            <div class="button-group">
                                <el-button type="primary" @click="convertColor">转换颜色</el-button>
                                <el-button type="success" @click="generateRandomColor">随机颜色</el-button>
                                <el-button type="info" @click="copyResult('color')">复制结果</el-button>
                                <el-button @click="clearColor">清空</el-button>
                            </div>
                            <div class="input-group">
                                <label class="input-label">转换结果：</label>
                                <div class="result-area" id="colorResult">{{ colorResult }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- 关于对话框 -->
            <el-dialog v-model="showAbout" title="关于工具集" width="500px">
                <div style="text-align: center;">
                    <img src="https://img.scmttec.com/gw/laiyuemiao/<EMAIL>" alt="Logo" style="width: 60px; height: 60px; margin-bottom: 1rem;">
                    <h3>现代化测试工具集</h3>
                    <p style="color: #666; margin: 1rem 0;">一个集成多种实用工具的现代化Web应用</p>

                    <el-divider></el-divider>

                    <div style="text-align: left;">
                        <h4>功能特性：</h4>
                        <ul style="padding-left: 1.5rem; color: #666;">
                            <li>Base64 编解码</li>
                            <li>JSON 格式化与压缩</li>
                            <li>URL 编解码</li>
                            <li>时间戳转换</li>
                            <li>MD5 加密</li>
                            <li>AES 加密解密</li>
                            <li>随机数生成</li>
                            <li>颜色转换工具</li>
                        </ul>

                        <h4 style="margin-top: 1rem;">技术栈：</h4>
                        <p style="color: #666;">Vue 3 + Element Plus + CryptoJS</p>

                        <h4 style="margin-top: 1rem;">版本信息：</h4>
                        <p style="color: #666;">v2.0.0 - 现代化重构版本</p>
                    </div>
                </div>

                <template #footer>
                    <el-button @click="showAbout = false">关闭</el-button>
                    <el-button type="primary" @click="showAbout = false">确定</el-button>
                </template>
            </el-dialog>

            <!-- 浮动操作按钮 -->
            <div class="floating-action">
                <el-button type="primary" circle @click="scrollToTop">
                    <el-icon><Top /></el-icon>
                </el-button>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    // 统计数据
                    stats: {
                        totalTools: 8,
                        usageCount: 0,
                        onlineUsers: Math.floor(Math.random() * 50) + 10
                    },

                    // Base64 相关
                    base64Input: '',
                    base64Result: '',

                    // JSON 相关
                    jsonInput: '',
                    jsonResult: '',

                    // URL 相关
                    urlInput: '',
                    urlResult: '',

                    // 时间戳相关
                    timestampInput: '',
                    timestampResult: '',

                    // MD5 相关
                    md5Input: '',
                    md5Result: '',

                    // AES 相关
                    aesKey: 'yuemiao_zW4NcAbR',
                    aesInput: '',
                    aesResult: '',

                    // 随机数相关
                    randomMin: 1,
                    randomMax: 100,
                    randomCount: 10,
                    randomResult: '',

                    // 颜色相关
                    colorInput: '',
                    colorResult: '',
                    colorPreview: '#ffffff',

                    // 其他
                    showAbout: false
                }
            },

            mounted() {
                // 初始化使用次数
                this.stats.usageCount = parseInt(localStorage.getItem('toolUsageCount') || '0');

                // 定时更新在线用户数
                setInterval(() => {
                    this.stats.onlineUsers = Math.floor(Math.random() * 50) + 10;
                }, 30000);

                // 显示欢迎消息
                this.$nextTick(() => {
                    ElMessage({
                        message: '欢迎使用现代化测试工具集！',
                        type: 'success',
                        duration: 3000
                    });
                });
            },

            methods: {
                // 增加使用次数
                incrementUsage() {
                    this.stats.usageCount++;
                    localStorage.setItem('toolUsageCount', this.stats.usageCount.toString());
                },

                // Base64 编码
                base64Encode() {
                    if (!this.base64Input.trim()) {
                        ElMessage.warning('请输入要编码的文本');
                        return;
                    }

                    try {
                        this.base64Result = btoa(unescape(encodeURIComponent(this.base64Input)));
                        this.incrementUsage();
                        ElMessage.success('Base64编码成功');
                    } catch (error) {
                        ElMessage.error('编码失败：' + error.message);
                    }
                },

                // Base64 解码
                base64Decode() {
                    if (!this.base64Input.trim()) {
                        ElMessage.warning('请输入要解码的Base64文本');
                        return;
                    }

                    try {
                        this.base64Result = decodeURIComponent(escape(atob(this.base64Input)));
                        this.incrementUsage();
                        ElMessage.success('Base64解码成功');
                    } catch (error) {
                        ElMessage.error('解码失败：请检查输入的Base64格式是否正确');
                    }
                },

                // 清空Base64
                clearBase64() {
                    this.base64Input = '';
                    this.base64Result = '';
                },

                // JSON 格式化
                formatJson() {
                    if (!this.jsonInput.trim()) {
                        ElMessage.warning('请输入JSON文本');
                        return;
                    }

                    try {
                        const parsed = JSON.parse(this.jsonInput);
                        this.jsonResult = JSON.stringify(parsed, null, 2);
                        this.incrementUsage();
                        ElMessage.success('JSON格式化成功');
                    } catch (error) {
                        ElMessage.error('JSON格式错误：' + error.message);
                    }
                },

                // JSON 压缩
                compressJson() {
                    if (!this.jsonInput.trim()) {
                        ElMessage.warning('请输入JSON文本');
                        return;
                    }

                    try {
                        const parsed = JSON.parse(this.jsonInput);
                        this.jsonResult = JSON.stringify(parsed);
                        this.incrementUsage();
                        ElMessage.success('JSON压缩成功');
                    } catch (error) {
                        ElMessage.error('JSON格式错误：' + error.message);
                    }
                },

                // 清空JSON
                clearJson() {
                    this.jsonInput = '';
                    this.jsonResult = '';
                },

                // URL 编码
                urlEncode() {
                    if (!this.urlInput.trim()) {
                        ElMessage.warning('请输入要编码的URL');
                        return;
                    }

                    try {
                        this.urlResult = encodeURIComponent(this.urlInput);
                        this.incrementUsage();
                        ElMessage.success('URL编码成功');
                    } catch (error) {
                        ElMessage.error('编码失败：' + error.message);
                    }
                },

                // URL 解码
                urlDecode() {
                    if (!this.urlInput.trim()) {
                        ElMessage.warning('请输入要解码的URL');
                        return;
                    }

                    try {
                        this.urlResult = decodeURIComponent(this.urlInput);
                        this.incrementUsage();
                        ElMessage.success('URL解码成功');
                    } catch (error) {
                        ElMessage.error('解码失败：' + error.message);
                    }
                },

                // 清空URL
                clearUrl() {
                    this.urlInput = '';
                    this.urlResult = '';
                },

                // 时间戳转日期
                timestampToDate() {
                    if (!this.timestampInput.trim()) {
                        ElMessage.warning('请输入时间戳');
                        return;
                    }

                    try {
                        const timestamp = parseInt(this.timestampInput);
                        const date = new Date(timestamp.toString().length === 10 ? timestamp * 1000 : timestamp);
                        this.timestampResult = date.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                        this.incrementUsage();
                        ElMessage.success('时间戳转换成功');
                    } catch (error) {
                        ElMessage.error('时间戳格式错误');
                    }
                },

                // 日期转时间戳
                dateToTimestamp() {
                    if (!this.timestampInput.trim()) {
                        ElMessage.warning('请输入日期时间');
                        return;
                    }

                    try {
                        const date = new Date(this.timestampInput);
                        if (isNaN(date.getTime())) {
                            throw new Error('Invalid date');
                        }
                        this.timestampResult = Math.floor(date.getTime() / 1000).toString();
                        this.incrementUsage();
                        ElMessage.success('日期转换成功');
                    } catch (error) {
                        ElMessage.error('日期格式错误，请使用标准日期格式');
                    }
                },

                // 获取当前时间戳
                getCurrentTimestamp() {
                    const now = Math.floor(Date.now() / 1000);
                    this.timestampResult = `当前时间戳: ${now}\n当前时间: ${new Date().toLocaleString('zh-CN')}`;
                    this.incrementUsage();
                    ElMessage.success('获取当前时间戳成功');
                },

                // 清空时间戳
                clearTimestamp() {
                    this.timestampInput = '';
                    this.timestampResult = '';
                },

                // MD5加密
                md5Encrypt() {
                    if (!this.md5Input.trim()) {
                        ElMessage.warning('请输入要加密的文本');
                        return;
                    }

                    try {
                        this.md5Result = CryptoJS.MD5(this.md5Input).toString();
                        this.incrementUsage();
                        ElMessage.success('MD5加密成功');
                    } catch (error) {
                        ElMessage.error('MD5加密失败：' + error.message);
                    }
                },

                // 清空MD5
                clearMd5() {
                    this.md5Input = '';
                    this.md5Result = '';
                },

                // AES加密
                aesEncrypt() {
                    if (!this.aesInput.trim()) {
                        ElMessage.warning('请输入要加密的文本');
                        return;
                    }

                    if (!this.aesKey.trim()) {
                        ElMessage.warning('请输入AES密钥');
                        return;
                    }

                    try {
                        const encrypted = CryptoJS.AES.encrypt(this.aesInput, this.aesKey).toString();
                        this.aesResult = encrypted;
                        this.incrementUsage();
                        ElMessage.success('AES加密成功');
                    } catch (error) {
                        ElMessage.error('AES加密失败：' + error.message);
                    }
                },

                // AES解密
                aesDecrypt() {
                    if (!this.aesInput.trim()) {
                        ElMessage.warning('请输入要解密的文本');
                        return;
                    }

                    if (!this.aesKey.trim()) {
                        ElMessage.warning('请输入AES密钥');
                        return;
                    }

                    try {
                        const decrypted = CryptoJS.AES.decrypt(this.aesInput, this.aesKey);
                        this.aesResult = decrypted.toString(CryptoJS.enc.Utf8);

                        if (!this.aesResult) {
                            throw new Error('解密失败，请检查密钥是否正确');
                        }

                        this.incrementUsage();
                        ElMessage.success('AES解密成功');
                    } catch (error) {
                        ElMessage.error('AES解密失败：' + error.message);
                    }
                },

                // 清空AES
                clearAes() {
                    this.aesInput = '';
                    this.aesResult = '';
                },

                // 生成随机数
                generateRandom() {
                    if (this.randomMin >= this.randomMax) {
                        ElMessage.warning('最小值必须小于最大值');
                        return;
                    }

                    const results = [];
                    for (let i = 0; i < this.randomCount; i++) {
                        const random = Math.floor(Math.random() * (this.randomMax - this.randomMin + 1)) + this.randomMin;
                        results.push(random);
                    }

                    this.randomResult = results.join(', ');
                    this.incrementUsage();
                    ElMessage.success(`生成了${this.randomCount}个随机数`);
                },

                // 生成UUID
                generateUUID() {
                    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                        const r = Math.random() * 16 | 0;
                        const v = c == 'x' ? r : (r & 0x3 | 0x8);
                        return v.toString(16);
                    });

                    this.randomResult = uuid;
                    this.incrementUsage();
                    ElMessage.success('UUID生成成功');
                },

                // 清空随机数
                clearRandom() {
                    this.randomResult = '';
                },

                // 转换颜色
                convertColor() {
                    if (!this.colorInput.trim()) {
                        ElMessage.warning('请输入颜色值');
                        return;
                    }

                    try {
                        let color = this.colorInput.trim();
                        let results = [];

                        // 如果是HEX格式
                        if (color.startsWith('#')) {
                            const hex = color.substring(1);
                            if (hex.length === 3 || hex.length === 6) {
                                const fullHex = hex.length === 3 ?
                                    hex.split('').map(c => c + c).join('') : hex;

                                const r = parseInt(fullHex.substring(0, 2), 16);
                                const g = parseInt(fullHex.substring(2, 4), 16);
                                const b = parseInt(fullHex.substring(4, 6), 16);

                                results.push(`HEX: #${fullHex.toUpperCase()}`);
                                results.push(`RGB: rgb(${r}, ${g}, ${b})`);
                                results.push(`HSL: ${this.rgbToHsl(r, g, b)}`);

                                this.colorPreview = `#${fullHex}`;
                            }
                        }
                        // 如果是RGB格式
                        else if (color.startsWith('rgb')) {
                            const match = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                            if (match) {
                                const r = parseInt(match[1]);
                                const g = parseInt(match[2]);
                                const b = parseInt(match[3]);

                                const hex = ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();

                                results.push(`HEX: #${hex}`);
                                results.push(`RGB: rgb(${r}, ${g}, ${b})`);
                                results.push(`HSL: ${this.rgbToHsl(r, g, b)}`);

                                this.colorPreview = `rgb(${r}, ${g}, ${b})`;
                            }
                        }

                        if (results.length > 0) {
                            this.colorResult = results.join('\n');
                            this.incrementUsage();
                            ElMessage.success('颜色转换成功');
                        } else {
                            throw new Error('不支持的颜色格式');
                        }
                    } catch (error) {
                        ElMessage.error('颜色格式错误：' + error.message);
                    }
                },

                // RGB转HSL
                rgbToHsl(r, g, b) {
                    r /= 255;
                    g /= 255;
                    b /= 255;

                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    let h, s, l = (max + min) / 2;

                    if (max === min) {
                        h = s = 0;
                    } else {
                        const d = max - min;
                        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

                        switch (max) {
                            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                            case g: h = (b - r) / d + 2; break;
                            case b: h = (r - g) / d + 4; break;
                        }
                        h /= 6;
                    }

                    return `hsl(${Math.round(h * 360)}, ${Math.round(s * 100)}%, ${Math.round(l * 100)}%)`;
                },

                // 生成随机颜色
                generateRandomColor() {
                    const r = Math.floor(Math.random() * 256);
                    const g = Math.floor(Math.random() * 256);
                    const b = Math.floor(Math.random() * 256);

                    const hex = ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();

                    this.colorInput = `#${hex}`;
                    this.colorPreview = `#${hex}`;
                    this.colorResult = `HEX: #${hex}\nRGB: rgb(${r}, ${g}, ${b})\nHSL: ${this.rgbToHsl(r, g, b)}`;

                    this.incrementUsage();
                    ElMessage.success('随机颜色生成成功');
                },

                // 清空颜色
                clearColor() {
                    this.colorInput = '';
                    this.colorResult = '';
                    this.colorPreview = '#ffffff';
                },

                // 复制结果
                async copyResult(type) {
                    let textToCopy = '';

                    switch(type) {
                        case 'base64':
                            textToCopy = this.base64Result;
                            break;
                        case 'json':
                            textToCopy = this.jsonResult;
                            break;
                        case 'url':
                            textToCopy = this.urlResult;
                            break;
                        case 'timestamp':
                            textToCopy = this.timestampResult;
                            break;
                        case 'md5':
                            textToCopy = this.md5Result;
                            break;
                        case 'aes':
                            textToCopy = this.aesResult;
                            break;
                        case 'random':
                            textToCopy = this.randomResult;
                            break;
                        case 'color':
                            textToCopy = this.colorResult;
                            break;
                    }

                    if (!textToCopy) {
                        ElMessage.warning('没有可复制的内容');
                        return;
                    }

                    try {
                        await navigator.clipboard.writeText(textToCopy);
                        ElMessage.success('复制成功');
                    } catch (error) {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = textToCopy;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        ElMessage.success('复制成功');
                    }
                },

                // 滚动到顶部
                scrollToTop() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
